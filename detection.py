import json
import cv2
import numpy as np
import onnxruntime as ort
import os


class Yolov8:
    def __init__(self, model, confidence_thres, iou_thres, classes):
        self.session = ort.InferenceSession(model, providers=['CUDAExecutionProvider', 'CPUExecutionProvider'])
        self.confidence_thres = confidence_thres
        self.iou_thres = iou_thres
        self.classes = classes
        self.model_inputs = self.session.get_inputs()

        # Store the shape of the input for later use
        input_shape = self.model_inputs[0].shape
        self.input_width = input_shape[2]
        self.input_height = input_shape[3]

    def addDic(self, dic, box, img_height, img_width):
        x1, y1, w, h = box
        dic['shapes'].append({"label": self.classes, "points": [(x1 / img_width, y1 / img_height),
                                                                ((x1 + w) / img_width,
                                                                 (y1 + h) / img_height)], "group_id": None,
                              "shape_type": "rectangle", "flags": {}, "other_data": {}})

    def predictPic(self, img_path, dic_path):
        dic = {}
        if not os.path.exists(img_path):
            print('img not exist!')
        img = cv2.imread(img_path)
        img_height, img_width = img.shape[:2]
        if not os.path.exists(dic_path):
            dic = {"version": "4.5.6", "flags": {}, "shapes": list(), "imagePath": img_path,
                   "imageHeight": img_height, "imageWidth": img_width}
        else:
            with open(dic_path) as f:
                dic = json.load(f)
        image_data = self.preProcess(img, dic)
        outputs = self.session.run(None, {self.model_inputs[0].name: image_data})
        dic = self.postProcess(img, outputs, dic)
        with open(dic_path, mode='w', encoding='utf-8') as file:
            json.dump(dic, file, indent=4)

    def preProcess(self, img, dic):
        img_height, img_width = img.shape[:2]
        dic["imageHeight"] = img_height
        dic["imageWidth"] = img_width
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        img = cv2.resize(img, (self.input_width, self.input_height))
        image_data = np.array(img) / 255.0
        image_data = np.transpose(image_data, (2, 0, 1))  # Channel first
        image_data = np.expand_dims(image_data, axis=0).astype(np.float32)
        return image_data

    def postProcess(self, img, output, dic):
        outputs = np.transpose(np.squeeze(output[0]))
        img_height, img_width = img.shape[:2]
        rows = outputs.shape[0]
        boxes = []
        scores = []
        class_ids = []
        x_factor = img_width / self.input_width
        y_factor = img_height / self.input_height
        for i in range(rows):
            classes_scores = outputs[i][4:]
            max_score = np.amax(classes_scores)
            if max_score >= self.confidence_thres:
                class_id = np.argmax(classes_scores)
                x, y, w, h = outputs[i][0], outputs[i][1], outputs[i][2], outputs[i][3]
                left = int((x - w / 2) * x_factor)
                top = int((y - h / 2) * y_factor)
                width = int(w * x_factor)
                height = int(h * y_factor)
                class_ids.append(class_id)
                scores.append(max_score)
                boxes.append([left, top, width, height])
        indices = cv2.dnn.NMSBoxes(boxes, scores, self.confidence_thres, self.iou_thres)
        #minimum = 60
        minimum=-1
        for i in indices:
            box = boxes[i]
            tmp = img[box[1]:box[1] + box[3], box[0]:box[0] + box[2], :]
            if cv2.mean(tmp)[0] > minimum or cv2.mean(tmp)[1] > minimum or cv2.mean(tmp)[2] > minimum:
                self.addDic(dic, box, img_height, img_width)
        return dic
