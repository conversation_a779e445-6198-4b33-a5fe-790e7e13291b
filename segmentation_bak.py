import os
import onnxruntime
import numpy as np
import cv2
import json

from getSkel import Xihua


class Segmentation:
    def __init__(self, onnx_model, classes):
        sess_gpu = onnxruntime.InferenceSession(onnx_model,
                                                providers=['CUDAExecutionProvider', 'CPUExecutionProvider'])
        in_name = [input.name for input in sess_gpu.get_inputs()][0]
        out_name = [output.name for output in sess_gpu.get_outputs()]
        self.session = {'predict': sess_gpu, 'in_name': in_name, 'out_name': out_name}
        self.classes = classes

    def preprocess(self, image):
        image = cv2.resize(np.array(image), (640, 640))
        img = image.copy().astype(np.float32)
        img_mean = [123.675, 116.28, 103.53]
        img_std = [58.395, 57.12, 57.375]
        mean = np.float64(np.array(img_mean).reshape(1, -1))
        stdinv = 1 / np.float64(np.array(img_std).reshape(1, -1))
        cv2.cvtColor(img, cv2.COLOR_BGR2RGB, img)
        cv2.subtract(img, mean, img)
        cv2.multiply(img, stdinv, img)
        img = img.astype(np.float32)
        # img = (image - np.array(img_mean).astype(np.float32)) / np.array(img_std).astype(np.float32)
        img = np.transpose(img, [2, 0, 1])
        img = np.expand_dims(img, axis=0)
        return img

    def predict(self, image):
        img = self.preprocess(image)
        res1 = self.session['predict'].run(self.session['out_name'], {self.session['in_name']: img})[0]
        res1 = np.squeeze(res1, axis=0)
        res2 = np.squeeze(res1, axis=0)
        return np.uint8(res2 * 255)

    def zhumaiSmooth(self, image):
        img = self.predict(image)
        height, width = img.shape

        points = []
        for x in range(width):
            y_coords = np.where(img[:, x] == 255)[0]  # 获取当前x坐标下所有白色像素的y坐标
            if len(y_coords) > 0:
                avg_y = np.mean(y_coords)
                points.append([x, avg_y])
        points = np.array(points)
        points = points.reshape((-1, 1, 2)).astype(np.int32)
        cv2.polylines(image, [points], isClosed=False, color=(0, 255, 0), thickness=2)
        # image[:, :][img] = (0, 0, 255)
        image[:, :][img != 0] = (0, 0, 255)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        return image

    def addDic(self, points, dic):
        dic["shapes"].append({"label": self.classes, "points": points, "group_id": None,
                              "shape_type": "polygon", "flags": {}, "other_data": {}})

    def predictPic(self, img_path, dic_path):
        dic = {}
        if not os.path.exists(img_path):
            print('img not exist!')
        img = cv2.imread(img_path)
        img_height, img_width = img.shape[:2]
        if not os.path.exists(dic_path):
            dic = {"version": "4.5.6", "flags": {}, "shapes": list(), "imagePath": img_path,
                   "imageHeight": img_height, "imageWidth": img_width}
        else:
            with open(dic_path) as f:
                dic = json.load(f)
        res = self.predict(img)
        dic = self.mask2json(res, dic)
        with open(dic_path, mode='w', encoding='utf-8') as file:
            json.dump(dic, file, indent=4)

    def mask2json(self, gray, dic):
        _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        for contour in contours:
            temp = list()
            for point in contour[:]:
                temp.append([int(point[0][0]) / 640, int(point[0][1]) / 640])
            self.addDic(temp, dic)
        return dic
