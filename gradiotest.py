import gradio as gr
from segmentation import Segmentation
import os
import cv2

os.environ["no_proxy"]="localhost,127.0.0.1,::1"
modelPath=r'F:\2024_04'
model=Segmentation(modelPath+'/zhumai.onnx',"zhumai")
def predict(image):
    image=cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
    image=cv2.resize(image,(640,640))
    #return cv2.resize(model.predict(image),dsize=(5028,2280))
    return cv2.resize(model.zhumaiSmooth(image), dsize=(5028, 2280))
demo=gr.Interface(predict,gr.Image(),"image")
demo.launch(share=True)