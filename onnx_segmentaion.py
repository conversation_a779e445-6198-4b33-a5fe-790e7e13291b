import os
import onnxruntime
import numpy as np
import cv2
import time
import json
device = 'gpu'


# device = 'cpu'
def load_model():
    weight_path = 'unirep.onnx'
    onnx_model = weight_path
    sess = onnxruntime.InferenceSession(onnx_model, providers=["CPUExecutionProvider"])  # cpu
    sess_gpu = onnxruntime.InferenceSession(onnx_model, providers=["CUDAExecutionProvider"])  # gpu
    return sess_gpu, sess
def predict(model, image):
    # image = cv2.resize(np.array(image), (1024,512))
    image = cv2.resize(np.array(image), (640, 640))
    img = image.copy().astype(np.float32)
    img_mean = [123.675, 116.28, 103.53]
    img_std = [58.395, 57.12, 57.375]
    mean = np.float64(np.array(img_mean).reshape(1, -1))
    stdinv = 1 / np.float64(np.array(img_std).reshape(1, -1))
    cv2.cvtColor(img, cv2.COLOR_BGR2RGB, img)
    cv2.subtract(img, mean, img)
    cv2.multiply(img, stdinv, img)
    img = img.astype(np.float32)
    # img = (image - np.array(img_mean).astype(np.float32)) / np.array(img_std).astype(np.float32)
    img = np.transpose(img, [2, 0, 1])
    img = np.expand_dims(img, axis=0)
    res1 = model['predict'].run(model['out_name'], {model['in_name']: img})[0]
    res1 = np.squeeze(res1, axis=0)
    res2 = np.squeeze(res1, axis=0)
    return np.uint8(res2 * 255)
def mask2json(gray, imgPath, img):
    _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    dic = {"version": "4.5.6", "flags": {}, "shapes": list(), "imagePath": imgPath,
           "imageHeight": img.shape[0], "imageWidth": img.shape[1]}
    for contour in contours:
        temp = list()
        for point in contour[:]:
            temp.append([int(point[0][0])/640, int(point[0][1])/640])
        dic["shapes"].append({"label": "2", "points": temp, "group_id": None,
                              "shape_type": "polygon", "flags": {}, "other_data": {}})
    return dic
def getJson(model,file):
    image = cv2.imread(file)
    res2 = predict(model, image)
    dic = mask2json(res2, file, image)
    with open(file.split('.')[0] + '.json', mode='w', encoding='utf-8') as file:
        json.dump(dic, file,indent=4)
def main():
    sess_gpu, sess_cpu = load_model()
    if device == 'cpu':
        in_name = [input.name for input in sess_cpu.get_inputs()][0]
        out_name = [output.name for output in sess_cpu.get_outputs()]
        model = {'predict': sess_cpu, 'in_name': in_name, 'out_name': out_name}
    else:
        in_name = [input.name for input in sess_gpu.get_inputs()][0]
        out_name = [output.name for output in sess_gpu.get_outputs()]
        model = {'predict': sess_gpu, 'in_name': in_name, 'out_name': out_name}

    path = os.getcwd()
    getJson(model,'2.bmp')


main()