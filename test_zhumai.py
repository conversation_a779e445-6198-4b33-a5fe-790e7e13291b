#!/usr/bin/env python3
"""
测试zhumai模型的完整功能
使用1.bmp图像和zhumai.onnx模型，生成1.json文件和可视化图像
🚀 包含性能分析功能
"""

import cv2
import numpy as np
import json
import os
import time
import psutil
from segmentation import Segmentation


def draw_points_on_image(image, contour_points_list, refined_points, zhengti_points, 
                        output_path="visualization.jpg"):
    """
    在原图大小的白底图像上用不同颜色绘制三类点集
    
    Args:
        image: 原始图像（用于获取尺寸）
        contour_points_list: zhumaidakai点集列表
        refined_points: zhum<PERSON>_youxiao点集
        zhengti_points: zhumai_zhengti点集
        output_path: 输出图像路径
    """
    # 获取原图尺寸
    if len(image.shape) == 3:
        img_height, img_width, _ = image.shape
    else:
        img_height, img_width = image.shape
    
    # 创建白底图像
    vis_image = np.ones((img_height, img_width, 3), dtype=np.uint8) * 255
    
    print(f"创建可视化图像，尺寸: {img_width}x{img_height}")
    
    # 绘制zhumaidakai轮廓（红色）
    for i, contour_points in enumerate(contour_points_list):
        if contour_points:
            # 将相对坐标转换为像素坐标
            pixel_points = []
            for point in contour_points:
                x_pixel = int(point[0] * img_width)
                y_pixel = int(point[1] * img_height)
                # 确保坐标在图像范围内
                x_pixel = max(0, min(x_pixel, img_width - 1))
                y_pixel = max(0, min(y_pixel, img_height - 1))
                pixel_points.append([x_pixel, y_pixel])
            
            if len(pixel_points) > 2:
                # 绘制多边形轮廓
                pts = np.array(pixel_points, dtype=np.int32)
                cv2.polylines(vis_image, [pts], isClosed=True, color=(0, 0, 255), thickness=2)  # 红色
                print(f"绘制zhumaidakai轮廓 {i+1}，包含 {len(pixel_points)} 个点")
    
    # 绘制zhumai_youxiao点集（绿色）
    if refined_points:
        prev_point = None
        for point in refined_points:
            x_pixel = int(point[0] * img_width)
            y_pixel = int(point[1] * img_height)
            # 确保坐标在图像范围内
            x_pixel = max(0, min(x_pixel, img_width - 1))
            y_pixel = max(0, min(y_pixel, img_height - 1))
            
            # 绘制点
            cv2.circle(vis_image, (x_pixel, y_pixel), 1, (0, 255, 0), -1)  # 绿色
            
            # 连接相邻点
            if prev_point is not None:
                cv2.line(vis_image, prev_point, (x_pixel, y_pixel), (0, 255, 0), 1)
            prev_point = (x_pixel, y_pixel)
        
        print(f"绘制zhumai_youxiao点集，包含 {len(refined_points)} 个点")
    
    # 绘制zhumai_zhengti点集（蓝色）
    if zhengti_points:
        prev_point = None
        for point in zhengti_points:
            x_pixel = int(point[0] * img_width)
            y_pixel = int(point[1] * img_height)
            # 确保坐标在图像范围内
            x_pixel = max(0, min(x_pixel, img_width - 1))
            y_pixel = max(0, min(y_pixel, img_height - 1))
            
            # 绘制点
            cv2.circle(vis_image, (x_pixel, y_pixel), 1, (255, 0, 0), -1)  # 蓝色
            
            # 连接相邻点
            if prev_point is not None:
                cv2.line(vis_image, prev_point, (x_pixel, y_pixel), (255, 0, 0), 1)
            prev_point = (x_pixel, y_pixel)
        
        print(f"绘制zhumai_zhengti点集，包含 {len(zhengti_points)} 个点")
    
    # 保存可视化图像
    cv2.imwrite(output_path, vis_image)
    print(f"可视化图像已保存到: {output_path}")
    
    return vis_image


class PerformanceProfiler:
    """性能分析器"""

    def __init__(self):
        self.start_time = None
        self.memory_start = None
        self.stages = {}

    def start_profiling(self):
        """开始性能分析"""
        self.start_time = time.time()
        self.memory_start = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        print(f"🔍 开始性能分析 - 初始内存: {self.memory_start:.2f} MB")

    def mark_stage(self, stage_name):
        """标记阶段"""
        current_time = time.time()
        current_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB

        if self.start_time:
            elapsed = current_time - self.start_time
            memory_used = current_memory - self.memory_start
            self.stages[stage_name] = {
                'elapsed_time': elapsed,
                'memory_used': memory_used,
                'timestamp': current_time
            }
            print(f"⏱️  {stage_name}: {elapsed:.3f}s, 内存: +{memory_used:.2f}MB")

    def get_summary(self):
        """获取性能总结"""
        if not self.stages:
            return "无性能数据"

        total_time = max(stage['elapsed_time'] for stage in self.stages.values())
        max_memory = max(stage['memory_used'] for stage in self.stages.values())

        summary = f"""
📊 性能分析报告:
├─ 总耗时: {total_time:.3f}s
├─ 峰值内存: +{max_memory:.2f}MB
└─ 各阶段详情:"""

        for stage_name, data in self.stages.items():
            summary += f"\n   ├─ {stage_name}: {data['elapsed_time']:.3f}s"

        return summary


def test_detailed_performance_analysis():
    """
    详细的性能分析测试：统计segmentation.py各个阶段的运行时间
    """
    print("🔬 === 详细性能分析测试 ===")

    # 文件路径配置
    image_path = "1.bmp"
    model_path = "zhumai.onnx"

    # 检查文件存在性
    if not os.path.exists(image_path) or not os.path.exists(model_path):
        print("❌ 测试文件不存在，跳过性能测试")
        return

    try:
        # 初始化模型
        print("📋 初始化模型...")
        model = Segmentation(model_path, "zhumai")
        image = cv2.imread(image_path)

        print("\n🚀 开始详细性能分析:")
        profiler = PerformanceProfiler()
        profiler.start_profiling()

        # 使用性能分析器测试get_json_data函数
        json_data = model.get_json_data(image, image_path, profiler=profiler)

        print(profiler.get_summary())

        # 分析结果
        shapes_count = len(json_data.get('shapes', []))
        print(f"\n📈 处理结果统计:")
        print(f"├─ 生成shapes数量: {shapes_count}")
        print(f"├─ JSON数据大小: {len(str(json_data))} 字符")
        print(f"└─ 模型推理次数: 1次 (已优化)")

        # 分析各个阶段的性能占比
        analyze_performance_breakdown(profiler)

    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        import traceback
        traceback.print_exc()


def analyze_performance_breakdown(profiler):
    """
    分析性能分解，识别瓶颈
    """
    print(f"\n🔍 === 性能瓶颈分析 ===")

    if not profiler.stages:
        print("❌ 无性能数据可分析")
        return

    # 计算各阶段耗时占比
    total_time = max(stage['elapsed_time'] for stage in profiler.stages.values())

    # 按耗时排序
    sorted_stages = sorted(profiler.stages.items(),
                          key=lambda x: x[1]['elapsed_time'],
                          reverse=True)

    print("📊 各阶段耗时排名:")
    for i, (stage_name, data) in enumerate(sorted_stages[:10], 1):  # 显示前10个最耗时的阶段
        percentage = (data['elapsed_time'] / total_time) * 100
        print(f"   {i:2d}. {stage_name}: {data['elapsed_time']:.3f}s ({percentage:.1f}%)")

    # 识别主要瓶颈
    print(f"\n💡 性能优化建议:")
    top_3_stages = sorted_stages[:3]
    for stage_name, data in top_3_stages:
        percentage = (data['elapsed_time'] / total_time) * 100
        if percentage > 20:
            print(f"   🔴 {stage_name} 占用{percentage:.1f}%时间，建议优先优化")
        elif percentage > 10:
            print(f"   🟡 {stage_name} 占用{percentage:.1f}%时间，可考虑优化")
        else:
            print(f"   🟢 {stage_name} 占用{percentage:.1f}%时间，性能良好")


def test_zhumai_segmentation():
    """
    测试zhumai分割的完整流程
    🚀 包含性能分析
    """
    # 文件路径配置
    image_path = "1.bmp"
    model_path = "zhumai.onnx"
    json_output_path = "1.json"
    visualization_output_path = "1_visualization.jpg"

    print("=== 开始测试zhumai分割功能 ===")

    # 检查输入文件是否存在
    if not os.path.exists(image_path):
        print(f"错误: 图像文件 {image_path} 不存在")
        return False

    if not os.path.exists(model_path):
        print(f"错误: 模型文件 {model_path} 不存在")
        return False

    try:
        # 性能分析器
        profiler = PerformanceProfiler()
        profiler.start_profiling()

        # 初始化分割模型
        print(f"加载模型: {model_path}")
        segmentation_model = Segmentation(model_path, "zhumai")
        profiler.mark_stage("模型初始化")

        # 读取图像
        print(f"读取图像: {image_path}")
        image = cv2.imread(image_path)
        if image is None:
            print(f"错误: 无法读取图像 {image_path}")
            return False

        print(f"图像尺寸: {image.shape}")
        profiler.mark_stage("图像加载")

        # 获取JSON数据 (优化后只调用一次模型推理，使用性能分析器)
        print("开始处理图像...")
        json_data = segmentation_model.get_json_data(image, image_path, profiler=profiler)
        profiler.mark_stage("JSON数据生成完成")

        # 保存JSON文件
        with open(json_output_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=4, ensure_ascii=False)
        print(f"JSON数据已保存到: {json_output_path}")
        profiler.mark_stage("JSON文件保存")

        # 提取三种点集用于可视化
        contour_points_list = []
        refined_points = []
        zhengti_points = []

        for shape in json_data["shapes"]:
            if shape["label"] == "zhumaidakai":
                contour_points_list.append(shape["points"])
            elif shape["label"] == "zhumai_youxiao":
                refined_points = shape["points"]
            elif shape["label"] == "zhumai_zhengti":
                zhengti_points = shape["points"]

        profiler.mark_stage("数据提取")

        # 生成可视化图像
        print("生成可视化图像...")
        draw_points_on_image(image, contour_points_list, refined_points, zhengti_points,
                           visualization_output_path)
        profiler.mark_stage("可视化生成")

        # 输出性能分析报告
        print(profiler.get_summary())

        # 输出统计信息
        print("\n=== 处理结果统计 ===")
        print(f"zhumaidakai轮廓数量: {len(contour_points_list)}")
        total_contour_points = sum(len(contour) for contour in contour_points_list)
        print(f"zhumaidakai总点数: {total_contour_points}")
        print(f"zhumai_youxiao点数: {len(refined_points)}")
        print(f"zhumai_zhengti点数: {len(zhengti_points)}")
        print(f"JSON shapes总数: {len(json_data['shapes'])}")

        # 性能优化效果分析
        print("\n🚀 === 性能优化效果 ===")
        print("✅ 优化前: 模型推理3次 (predict + get_zhumai_youxiao + get_zhumai_zhengti)")
        print("✅ 优化后: 模型推理1次 (get_json_data统一调用)")
        print("📈 理论性能提升: ~3倍")
        print("💾 内存使用优化: 避免重复存储中间结果")

        print("\n=== 测试完成 ===")
        print("输出文件:")
        print(f"  - JSON数据: {json_output_path}")
        print(f"  - 可视化图像: {visualization_output_path}")
        print("颜色说明:")
        print("  - 红色: zhumaidakai轮廓")
        print("  - 绿色: zhumai_youxiao点集")
        print("  - 蓝色: zhumai_zhengti点集")

        return True

    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def analyze_segmentation_bottlenecks():
    """
    分析segmentation.py的性能瓶颈
    """
    print("\n🔍 === Segmentation性能瓶颈分析 ===")

    bottlenecks = {
        "模型推理": {
            "描述": "ONNX模型的前向推理过程",
            "优化前": "被调用3次",
            "优化后": "只调用1次",
            "影响": "高 - 通常是最耗时的操作"
        },
        "图像预处理": {
            "描述": "resize, 归一化, 转换等操作",
            "优化前": "每次推理都重复执行",
            "优化后": "只执行1次",
            "影响": "中 - 相对较快但仍有开销"
        },
        "轮廓检测": {
            "描述": "cv2.findContours操作",
            "优化前": "在predict中执行1次",
            "优化后": "仍然执行1次",
            "影响": "低 - 相对较快"
        },
        "骨架细化": {
            "描述": "Xihua算法处理",
            "优化前": "执行1次",
            "优化后": "仍然执行1次",
            "影响": "中 - 迭代算法，有一定开销"
        },
        "坐标转换": {
            "描述": "像素坐标与相对坐标转换",
            "优化前": "多次重复转换",
            "优化后": "减少了重复转换",
            "影响": "低 - 简单数学运算"
        }
    }

    for name, info in bottlenecks.items():
        print(f"\n📊 {name}:")
        print(f"   ├─ 描述: {info['描述']}")
        print(f"   ├─ 优化前: {info['优化前']}")
        print(f"   ├─ 优化后: {info['优化后']}")
        print(f"   └─ 性能影响: {info['影响']}")

    print(f"\n💡 主要优化点:")
    print(f"   1. ✅ 消除重复模型推理 (最重要)")
    print(f"   2. ✅ 减少重复图像预处理")
    print(f"   3. ✅ 优化数据传递流程")
    print(f"   4. ✅ 遵循DRY原则，避免重复计算")


if __name__ == "__main__":
    # 运行详细性能分析测试
    test_detailed_performance_analysis()

    # 运行完整功能测试
    success = test_zhumai_segmentation()

    # 分析性能瓶颈
    analyze_segmentation_bottlenecks()

    if success:
        print("\n✅ 所有测试成功完成")
    else:
        print("\n❌ 测试失败")
