#!/usr/bin/env python3
"""
测试zhumai模型的完整功能
使用1.bmp图像和zhumai.onnx模型，生成1.json文件和可视化图像
"""

import cv2
import numpy as np
import json
import os
from segmentation import Segmentation


def draw_points_on_image(image, contour_points_list, refined_points, zhengti_points, 
                        output_path="visualization.jpg"):
    """
    在原图大小的白底图像上用不同颜色绘制三类点集
    
    Args:
        image: 原始图像（用于获取尺寸）
        contour_points_list: zhumaidakai点集列表
        refined_points: zhumai_youxiao点集
        zhengti_points: zhumai_zhengti点集
        output_path: 输出图像路径
    """
    # 获取原图尺寸
    if len(image.shape) == 3:
        img_height, img_width, _ = image.shape
    else:
        img_height, img_width = image.shape
    
    # 创建白底图像
    vis_image = np.ones((img_height, img_width, 3), dtype=np.uint8) * 255
    
    print(f"创建可视化图像，尺寸: {img_width}x{img_height}")
    
    # 绘制zhumaidakai轮廓（红色）
    for i, contour_points in enumerate(contour_points_list):
        if contour_points:
            # 将相对坐标转换为像素坐标
            pixel_points = []
            for point in contour_points:
                x_pixel = int(point[0] * img_width)
                y_pixel = int(point[1] * img_height)
                # 确保坐标在图像范围内
                x_pixel = max(0, min(x_pixel, img_width - 1))
                y_pixel = max(0, min(y_pixel, img_height - 1))
                pixel_points.append([x_pixel, y_pixel])
            
            if len(pixel_points) > 2:
                # 绘制多边形轮廓
                pts = np.array(pixel_points, dtype=np.int32)
                cv2.polylines(vis_image, [pts], isClosed=True, color=(0, 0, 255), thickness=2)  # 红色
                print(f"绘制zhumaidakai轮廓 {i+1}，包含 {len(pixel_points)} 个点")
    
    # 绘制zhumai_youxiao点集（绿色）
    if refined_points:
        prev_point = None
        for point in refined_points:
            x_pixel = int(point[0] * img_width)
            y_pixel = int(point[1] * img_height)
            # 确保坐标在图像范围内
            x_pixel = max(0, min(x_pixel, img_width - 1))
            y_pixel = max(0, min(y_pixel, img_height - 1))
            
            # 绘制点
            cv2.circle(vis_image, (x_pixel, y_pixel), 1, (0, 255, 0), -1)  # 绿色
            
            # 连接相邻点
            if prev_point is not None:
                cv2.line(vis_image, prev_point, (x_pixel, y_pixel), (0, 255, 0), 1)
            prev_point = (x_pixel, y_pixel)
        
        print(f"绘制zhumai_youxiao点集，包含 {len(refined_points)} 个点")
    
    # 绘制zhumai_zhengti点集（蓝色）
    if zhengti_points:
        prev_point = None
        for point in zhengti_points:
            x_pixel = int(point[0] * img_width)
            y_pixel = int(point[1] * img_height)
            # 确保坐标在图像范围内
            x_pixel = max(0, min(x_pixel, img_width - 1))
            y_pixel = max(0, min(y_pixel, img_height - 1))
            
            # 绘制点
            cv2.circle(vis_image, (x_pixel, y_pixel), 1, (255, 0, 0), -1)  # 蓝色
            
            # 连接相邻点
            if prev_point is not None:
                cv2.line(vis_image, prev_point, (x_pixel, y_pixel), (255, 0, 0), 1)
            prev_point = (x_pixel, y_pixel)
        
        print(f"绘制zhumai_zhengti点集，包含 {len(zhengti_points)} 个点")
    
    # 保存可视化图像
    cv2.imwrite(output_path, vis_image)
    print(f"可视化图像已保存到: {output_path}")
    
    return vis_image


def test_zhumai_segmentation():
    """
    测试zhumai分割的完整流程
    """
    # 文件路径配置
    image_path = "1.bmp"
    model_path = "zhumai.onnx"
    json_output_path = "1.json"
    visualization_output_path = "1_visualization.jpg"
    
    print("=== 开始测试zhumai分割功能 ===")
    
    # 检查输入文件是否存在
    if not os.path.exists(image_path):
        print(f"错误: 图像文件 {image_path} 不存在")
        return False
    
    if not os.path.exists(model_path):
        print(f"错误: 模型文件 {model_path} 不存在")
        return False
    
    try:
        # 初始化分割模型
        print(f"加载模型: {model_path}")
        segmentation_model = Segmentation(model_path, "zhumai")
        print("模型加载成功")
        
        # 读取图像
        print(f"读取图像: {image_path}")
        image = cv2.imread(image_path)
        if image is None:
            print(f"错误: 无法读取图像 {image_path}")
            return False
        
        print(f"图像尺寸: {image.shape}")
        
        # 获取JSON数据
        print("开始处理图像...")
        json_data = segmentation_model.get_json_data(image, image_path)
        
        # 保存JSON文件
        with open(json_output_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=4, ensure_ascii=False)
        print(f"JSON数据已保存到: {json_output_path}")
        
        # 提取三种点集用于可视化
        contour_points_list = []
        refined_points = []
        zhengti_points = []
        
        for shape in json_data["shapes"]:
            if shape["label"] == "zhumaidakai":
                contour_points_list.append(shape["points"])
            elif shape["label"] == "zhumai_youxiao":
                refined_points = shape["points"]
            elif shape["label"] == "zhumai_zhengti":
                zhengti_points = shape["points"]
        
        # 生成可视化图像
        print("生成可视化图像...")
        draw_points_on_image(image, contour_points_list, refined_points, zhengti_points, 
                           visualization_output_path)
        
        # 输出统计信息
        print("\n=== 处理结果统计 ===")
        print(f"zhumaidakai轮廓数量: {len(contour_points_list)}")
        total_contour_points = sum(len(contour) for contour in contour_points_list)
        print(f"zhumaidakai总点数: {total_contour_points}")
        print(f"zhumai_youxiao点数: {len(refined_points)}")
        print(f"zhumai_zhengti点数: {len(zhengti_points)}")
        print(f"JSON shapes总数: {len(json_data['shapes'])}")
        
        print("\n=== 测试完成 ===")
        print("输出文件:")
        print(f"  - JSON数据: {json_output_path}")
        print(f"  - 可视化图像: {visualization_output_path}")
        print("颜色说明:")
        print("  - 红色: zhumaidakai轮廓")
        print("  - 绿色: zhumai_youxiao点集")
        print("  - 蓝色: zhumai_zhengti点集")
        
        return True
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_zhumai_segmentation()
    if success:
        print("\n✅ 测试成功完成")
    else:
        print("\n❌ 测试失败")
