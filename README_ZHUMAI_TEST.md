# Zhumai分割测试程序使用说明

## 概述

本文档说明如何使用新增的`get_json_data`函数和`test_zhumai.py`测试程序来处理zhumai分割任务。

## 新增功能

### 1. Segmentation.get_json_data() 函数

**功能**: 组合三种点集数据，生成完整的JSON结构

**方法签名**:
```python
def get_json_data(self, image, image_path=None):
    """
    组合三种点集数据，生成完整的JSON结构
    
    Args:
        image: 输入图像 (numpy.ndarray)
        image_path: 图像路径（可选，用于JSON中的imagePath字段）
        
    Returns:
        dict: 包含三种点集的完整JSON数据结构
    """
```

**返回的JSON结构**:
```json
{
    "version": "5.0.1",
    "flags": {},
    "shapes": [
        {
            "label": "zhumaidakai",
            "points": [[x1, y1], [x2, y2], ...],
            "group_id": null,
            "shape_type": "polygon",
            "flags": {}
        },
        {
            "label": "zhumai_youxiao", 
            "points": [[x1, y1], [x2, y2], ...],
            "group_id": null,
            "shape_type": "linestrip",
            "flags": {}
        },
        {
            "label": "zhumai_zhengti",
            "points": [[x1, y1], [x2, y2], ...],
            "group_id": null,
            "shape_type": "linestrip", 
            "flags": {}
        }
    ],
    "imagePath": "图像路径",
    "imageHeight": 图像高度,
    "imageWidth": 图像宽度
}
```

### 2. test_zhumai.py 测试程序

**功能**: 
- 使用1.bmp图像和zhumai.onnx模型进行测试
- 生成1.json文件包含完整的分割结果
- 生成可视化图像显示三类点集

**输入文件要求**:
- `1.bmp`: 待处理的图像文件
- `zhumai.onnx`: 训练好的ONNX模型文件

**输出文件**:
- `1.json`: 包含三种点集的JSON数据
- `1_visualization.jpg`: 可视化图像

## 使用方法

### 1. 准备文件

确保工作目录下有以下文件：
```
e:\downloads\LsDownload\onnx_predict\
├── 1.bmp              # 输入图像
├── zhumai.onnx        # ONNX模型
├── segmentation.py    # 分割类（已修改）
└── test_zhumai.py     # 测试程序
```

### 2. 运行测试

```bash
cd e:\downloads\LsDownload\onnx_predict
python test_zhumai.py
```

### 3. 查看结果

程序运行成功后会生成：
- `1.json`: JSON格式的分割结果
- `1_visualization.jpg`: 可视化图像

## 可视化说明

生成的可视化图像使用不同颜色标识三类点集：

- **红色**: zhumaidakai轮廓（polygon类型）
- **绿色**: zhumai_youxiao点集（linestrip类型）  
- **蓝色**: zhumai_zhengti点集（linestrip类型）

## 代码示例

### 直接使用get_json_data函数

```python
import cv2
from segmentation import Segmentation

# 初始化模型
model = Segmentation("zhumai.onnx", "zhumai")

# 读取图像
image = cv2.imread("1.bmp")

# 获取JSON数据
json_data = model.get_json_data(image, "1.bmp")

# 保存JSON文件
import json
with open("output.json", 'w', encoding='utf-8') as f:
    json.dump(json_data, f, indent=4, ensure_ascii=False)
```

### 分别获取三种点集

```python
# 获取三种点集
contour_points_list = model.predict(image)           # zhumaidakai
refined_points = model.get_zhumai_youxiao(image)     # zhumai_youxiao  
zhengti_points = model.get_zhumai_zhengti(image)     # zhumai_zhengti

print(f"轮廓数量: {len(contour_points_list)}")
print(f"细化点数: {len(refined_points)}")
print(f"整体点数: {len(zhengti_points)}")
```

## 设计原则遵循

### DRY (Don't Repeat Yourself)
- `get_json_data`函数复用了现有的三个函数
- 避免重复的JSON结构构建代码

### KISS (Keep It Simple, Stupid)  
- 函数职责单一，只负责组装JSON数据
- 测试程序逻辑清晰，易于理解

### SOLID原则
- **单一职责**: `get_json_data`只负责数据组装
- **开闭原则**: 不修改现有函数，通过组合实现新功能
- **依赖倒置**: 依赖于抽象的接口而非具体实现

## 错误处理

程序包含完善的错误处理：
- 检查输入文件是否存在
- 验证图像是否能正确读取
- 捕获并报告运行时异常
- 提供详细的错误信息和堆栈跟踪

## 性能考虑

- 函数调用顺序优化，避免重复计算
- 内存使用合理，及时释放临时变量
- 图像处理使用高效的OpenCV操作

## 扩展性

代码设计具有良好的扩展性：
- 可以轻松添加新的点集类型
- JSON结构可以方便地扩展新字段
- 可视化功能可以添加更多颜色和样式选项
