#!/usr/bin/env python3
"""
使用修改后的Segmentation类的示例
"""

import cv2
import json
from segmentation import Segmentation

def create_complete_json_output(model, image_path, output_json_path):
    """
    创建完整的JSON输出，包含所有三种类型的数据
    """
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        return
    
    img_height, img_width = image.shape[:2]
    
    # 获取三种不同的点集
    zhumaidakai_points = model.predict(image)  # mask轮廓点
    zhumai_youxiao_points = model.get_zhumai_youxiao(image)  # 细化后的点集
    zhumai_zhengti_points = model.get_zhumai_zhengti(image)  # 拟合修复后的点集
    
    # 创建JSON结构
    json_data = {
        "version": "5.0.1",
        "flags": {},
        "shapes": [],
        "imagePath": image_path,
        "imageHeight": img_height,
        "imageWidth": img_width
    }
    
    # 添加zhumaidakai数据（可能有多个轮廓）
    for i, contour_points in enumerate(zhumaidakai_points):
        shape_data = {
            "label": "zhumaidakai",
            "points": contour_points,
            "group_id": None,
            "shape_type": "polygon",
            "flags": {}
        }
        json_data["shapes"].append(shape_data)
    
    # 添加zhumai_youxiao数据
    if zhumai_youxiao_points:
        shape_data = {
            "label": "zhumai_youxiao",
            "points": zhumai_youxiao_points,
            "group_id": None,
            "shape_type": "linestrip",
            "flags": {}
        }
        json_data["shapes"].append(shape_data)
    
    # 添加zhumai_zhengti数据
    if zhumai_zhengti_points:
        shape_data = {
            "label": "zhumai_zhengti",
            "points": zhumai_zhengti_points,
            "group_id": None,
            "shape_type": "linestrip",
            "flags": {}
        }
        json_data["shapes"].append(shape_data)
    
    # 保存JSON文件
    with open(output_json_path, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, indent=4, ensure_ascii=False)
    
    print(f"JSON文件已保存到: {output_json_path}")
    print(f"包含 {len(zhumaidakai_points)} 个轮廓")
    print(f"zhumai_youxiao包含 {len(zhumai_youxiao_points)} 个点")
    print(f"zhumai_zhengti包含 {len(zhumai_zhengti_points)} 个点")

def process_single_image(model_path, class_name, image_path, output_json_path):
    """
    处理单张图像的完整流程
    """
    try:
        # 初始化模型
        model = Segmentation(model_path, class_name)
        
        # 创建完整的JSON输出
        create_complete_json_output(model, image_path, output_json_path)
        
    except Exception as e:
        print(f"处理图像时出错: {e}")

def batch_process_images(model_path, class_name, image_dir, output_dir):
    """
    批量处理图像
    """
    import os
    
    try:
        # 初始化模型
        model = Segmentation(model_path, class_name)
        
        # 获取所有图像文件
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        image_files = []
        
        for file in os.listdir(image_dir):
            if any(file.lower().endswith(ext) for ext in image_extensions):
                image_files.append(file)
        
        print(f"找到 {len(image_files)} 个图像文件")
        
        # 处理每个图像
        for i, image_file in enumerate(image_files):
            image_path = os.path.join(image_dir, image_file)
            output_json_path = os.path.join(output_dir, 
                                          os.path.splitext(image_file)[0] + '.json')
            
            print(f"处理 {i+1}/{len(image_files)}: {image_file}")
            create_complete_json_output(model, image_path, output_json_path)
        
        print("批量处理完成")
        
    except Exception as e:
        print(f"批量处理时出错: {e}")

def main():
    """
    主函数 - 使用示例
    """
    # 配置参数
    model_path = "path/to/your/model.onnx"  # 替换为实际的模型路径
    class_name = "zhumai"  # 类别名称
    
    # 示例1: 处理单张图像
    print("=== 示例1: 处理单张图像 ===")
    image_path = "path/to/your/image.jpg"  # 替换为实际的图像路径
    output_json_path = "output.json"
    
    # process_single_image(model_path, class_name, image_path, output_json_path)
    
    # 示例2: 批量处理图像
    print("\n=== 示例2: 批量处理图像 ===")
    image_dir = "path/to/your/images/"  # 替换为实际的图像目录
    output_dir = "path/to/output/jsons/"  # 替换为实际的输出目录
    
    # batch_process_images(model_path, class_name, image_dir, output_dir)
    
    print("\n=== 使用说明 ===")
    print("1. 将model_path替换为您的ONNX模型文件路径")
    print("2. 将image_path替换为要处理的图像文件路径")
    print("3. 运行脚本即可生成包含三种数据的JSON文件")
    print("4. JSON文件格式与提供的示例文件完全一致")

if __name__ == "__main__":
    main()
