import onnxruntime as ort
import numpy as np
import cv2

class Classification:
    def __init__(self, model):
        self.session=ort.InferenceSession(model, providers=['CUDAExecutionProvider', 'CPUExecutionProvider'])
        self.model_inputs = self.session.get_inputs()
    def preProcess(self,image):
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        # 调整图像大小为224x224
        image = cv2.resize(image, (224, 224))
        # 转换为浮点型并归一化
        image = image.astype(np.float32) / 255.0
        # 标准化
        mean = np.array([0.485, 0.456, 0.406])
        std = np.array([0.229, 0.224, 0.225])
        image = (image - mean) / std
        # 转换为CHW格式
        image = np.transpose(image, (2, 0, 1))
        # 添加批次维度
        image = np.expand_dims(image, axis=0).astype(np.float32)
        return image
    def predictImage(self,image):
        img=self.preProcess(image)
        inputs = {self.session.get_inputs()[0].name: img}
        outputs = self.session.run(None, inputs)
        # 获取预测结果
        preds = np.argmax(outputs[0], axis=1)
        return preds[0]


model=Classification(r'F:\2024_06\qrCodeClassify.onnx')
img=cv2.imread(r'F:\2024_06\classification\dataset\train\0\1_1.jpg')
print(model.predictImage(img))