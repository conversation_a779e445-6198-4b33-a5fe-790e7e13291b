import os
import onnxruntime
import numpy as np
import cv2
import json

def VThin(image, array):
    h,w = image.shape
    NEXT = 1
    for i in range(h):
        for j in range(w):
            if NEXT == 0:
                NEXT = 1
            else:
                M = image[i, j - 1] + image[i, j] + image[i, j + 1] if 0 < j < w - 1 else 1
                if image[i, j] == 0 and M != 0:
                    a = [0] * 9
                    for k in range(3):
                        for l in range(3):
                            if -1 < (i - 1 + k) < h and -1 < (j - 1 + l) < w and image[i - 1 + k, j - 1 + l] == 255:
                                a[k * 3 + l] = 1
                    sum = a[0] * 1 + a[1] * 2 + a[2] * 4 + a[3] * 8 + a[5] * 16 + a[6] * 32 + a[7] * 64 + a[8] * 128
                    image[i, j] = array[sum] * 255
                    if array[sum] == 1:
                        NEXT = 0
    return image


def HThin(image, array):
    h,w = image.shape
    NEXT = 1
    for j in range(w):
        for i in range(h):
            if NEXT == 0:
                NEXT = 1
            else:
                M = image[i - 1, j] + image[i, j] + image[i + 1, j] if 0 < i < h - 1 else 1
                if image[i, j] == 0 and M != 0:
                    a = [0] * 9
                    for k in range(3):
                        for l in range(3):
                            if -1 < (i - 1 + k) < h and -1 < (j - 1 + l) < w and image[i - 1 + k, j - 1 + l] == 255:
                                a[k * 3 + l] = 1
                    sum = a[0] * 1 + a[1] * 2 + a[2] * 4 + a[3] * 8 + a[5] * 16 + a[6] * 32 + a[7] * 64 + a[8] * 128
                    image[i, j] = array[sum] * 255
                    if array[sum] == 1:
                        NEXT = 0
    return image


def Xihua(image,num=10):
    array = [0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1,
             1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1,
             0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1,
             1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1,
             1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
             0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
             1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 1, 0, 1,
             0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
             0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1,
             1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1,
             0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1,
             1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0,
             1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
             1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0,
             1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 1, 0, 0,
             1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 0, 0, 0]
    for i in range(num):
        image=VThin(image, array)
        image=HThin(image, array)
    return image


class Segmentation:
    def __init__(self, onnx_model, classes):
        sess_gpu = onnxruntime.InferenceSession(onnx_model,
                                                providers=['CUDAExecutionProvider', 'CPUExecutionProvider'])
        in_name = [input.name for input in sess_gpu.get_inputs()][0]
        out_name = [output.name for output in sess_gpu.get_outputs()]
        self.session = {'predict': sess_gpu, 'in_name': in_name, 'out_name': out_name}
        self.classes = classes

    def preprocess(self, image):
        image = cv2.resize(np.array(image), (640, 640))
        img = image.copy().astype(np.float32)
        img_mean = [123.675, 116.28, 103.53]
        img_std = [58.395, 57.12, 57.375]
        mean = np.float64(np.array(img_mean).reshape(1, -1))
        stdinv = 1 / np.float64(np.array(img_std).reshape(1, -1))
        cv2.cvtColor(img, cv2.COLOR_BGR2RGB, img)
        cv2.subtract(img, mean, img)
        cv2.multiply(img, stdinv, img)
        img = img.astype(np.float32)
        # img = (image - np.array(img_mean).astype(np.float32)) / np.array(img_std).astype(np.float32)
        img = np.transpose(img, [2, 0, 1])
        img = np.expand_dims(img, axis=0)
        return img

    def predict(self, image):
        img = self.preprocess(image)
        res1 = self.session['predict'].run(self.session['out_name'], {self.session['in_name']: img})[0]
        res1 = np.squeeze(res1, axis=0)
        res2 = np.squeeze(res1, axis=0)
        print(np.sum(res2==1))

        # 将mask转换为轮廓点的相对点集
        mask = np.uint8(res2 * 255)
        _, binary = cv2.threshold(mask, 127, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        contour_points_list = []
        for contour in contours:
            points = []
            for point in contour:
                # 转换为相对640*640的比例值
                x_ratio = float(point[0][0]) / 640.0
                y_ratio = float(point[0][1]) / 640.0
                points.append([x_ratio, y_ratio])
            contour_points_list.append(points)

        return contour_points_list

    def get_zhumai_youxiao(self, image):
        """
        获取细化后的相对点集（相对640*640的比例值）
        返回格式如JSON中的"zhumai_youxiao"的shape
        """
        # 首先获取mask轮廓点
        contour_points_list = self.predict(image)

        if not contour_points_list:
            return []

        # 对于每个轮廓，进行细化处理
        refined_points = []
        for contour_points in contour_points_list:
            # 将相对坐标转换回像素坐标进行细化处理
            pixel_points = []
            for point in contour_points:
                x_pixel = int(point[0] * 640)
                y_pixel = int(point[1] * 640)
                pixel_points.append([x_pixel, y_pixel])

            # 创建mask用于细化
            mask = np.zeros((640, 640), dtype=np.uint8)
            if len(pixel_points) > 2:
                pixel_points_array = np.array(pixel_points, dtype=np.int32)
                cv2.fillPoly(mask, [pixel_points_array], 255)

            # 进行细化处理
            mask = cv2.bitwise_not(mask)
            mask_copy = mask.copy()
            refined_mask = Xihua(mask_copy)

            # 提取细化后的骨架点
            black_pixels = np.column_stack(np.where(refined_mask == 0))

            if len(black_pixels) == 0:
                continue

            # 按顺序排列骨架点
            y_min, x_min = black_pixels.min(axis=0)
            y_max, x_max = black_pixels.max(axis=0)

            if y_max - y_min <= x_max - x_min:
                # 按x坐标排序
                black_pixels = black_pixels[black_pixels[:, 1].argsort()]
            else:
                # 按y坐标排序
                black_pixels = black_pixels[black_pixels[:, 0].argsort()]

            # 转换为相对坐标
            skeleton_points = []
            for pixel in black_pixels:
                y, x = pixel
                x_ratio = float(x) / 640.0
                y_ratio = float(y) / 640.0
                skeleton_points.append([x_ratio, y_ratio])

            refined_points.extend(skeleton_points)

        return refined_points

    def get_zhumai_zhengti(self, image):
        """
        根据get_zhumai_youxiao的结果进行拟合处理，返回修复后的相对点集
        返回格式如JSON中的"zhumai_zhengti"的shape
        """
        # 获取细化后的点集
        youxiao_points = self.get_zhumai_youxiao(image)

        if not youxiao_points:
            return []

        # 将相对坐标转换为像素坐标进行处理
        pixel_points = []
        for point in youxiao_points:
            x_pixel = int(point[0] * 640)
            y_pixel = int(point[1] * 640)
            pixel_points.append([y_pixel, x_pixel])  # 注意这里是[y, x]格式

        pixel_points = np.array(pixel_points)

        if len(pixel_points) < 2:
            return youxiao_points

        # 找到断点并进行连接修复
        y_min, x_min = pixel_points.min(axis=0)
        y_max, x_max = pixel_points.max(axis=0)

        chainPoints = []
        if y_max - y_min <= x_max - x_min:
            # 按x坐标排序
            sorted_points = pixel_points[pixel_points[:, 1].argsort()]
            for i in range(len(sorted_points) - 1):
                if sorted_points[i+1][1] - sorted_points[i][1] > 2:
                    chainPoints.append([sorted_points[i], sorted_points[i+1]])
        else:
            # 按y坐标排序
            sorted_points = pixel_points[pixel_points[:, 0].argsort()]
            for i in range(len(sorted_points) - 1):
                if sorted_points[i+1][0] - sorted_points[i][0] > 2:
                    chainPoints.append([sorted_points[i], sorted_points[i+1]])

        # 创建修复后的图像
        repaired_mask = np.zeros((640, 640), dtype=np.uint8)

        # 绘制原始骨架点
        for point in pixel_points:
            y, x = point
            if 0 <= y < 640 and 0 <= x < 640:
                repaired_mask[y, x] = 255

        # 连接断点
        for chain in chainPoints:
            start_point = (int(chain[0][1]), int(chain[0][0]))  # (x, y)
            end_point = (int(chain[1][1]), int(chain[1][0]))    # (x, y)
            cv2.line(repaired_mask, start_point, end_point, 255, 1)

        # 提取修复后的骨架点
        repaired_pixels = np.column_stack(np.where(repaired_mask == 255))

        # 按顺序排列
        if len(repaired_pixels) > 0:
            y_min, x_min = repaired_pixels.min(axis=0)
            y_max, x_max = repaired_pixels.max(axis=0)

            if y_max - y_min <= x_max - x_min:
                repaired_pixels = repaired_pixels[repaired_pixels[:, 1].argsort()]
            else:
                repaired_pixels = repaired_pixels[repaired_pixels[:, 0].argsort()]

        # 转换为相对坐标
        zhengti_points = []
        for pixel in repaired_pixels:
            y, x = pixel
            x_ratio = float(x) / 640.0
            y_ratio = float(y) / 640.0
            zhengti_points.append([x_ratio, y_ratio])

        return zhengti_points

    def get_json_data(self, image, image_path=None):
        """
        组合三种点集数据，生成完整的JSON结构

        Args:
            image: 输入图像
            image_path: 图像路径（可选，用于JSON中的imagePath字段）

        Returns:
            dict: 包含三种点集的完整JSON数据结构
        """
        # 获取图像尺寸
        if len(image.shape) == 3:
            img_height, img_width, _ = image.shape
        else:
            img_height, img_width = image.shape

        # 获取三种点集数据
        contour_points_list = self.predict(image)  # zhumaidakai数据
        refined_points = self.get_zhumai_youxiao(image)  # zhumai_youxiao数据
        zhengti_points = self.get_zhumai_zhengti(image)  # zhumai_zhengti数据

        # 构建JSON数据结构
        json_data = {
            "version": "5.0.1",
            "flags": {},
            "shapes": [],
            "imagePath": image_path if image_path else "",
            "imageHeight": img_height,
            "imageWidth": img_width
        }

        # 添加zhumaidakai数据（可能有多个轮廓）
        for i, contour_points in enumerate(contour_points_list):
            if contour_points:  # 确保轮廓不为空
                shape_data = {
                    "label": "zhumaidakai",
                    "points": contour_points,
                    "group_id": None,
                    "shape_type": "polygon",
                    "flags": {}
                }
                json_data["shapes"].append(shape_data)

        # 添加zhumai_youxiao数据
        if refined_points:
            shape_data = {
                "label": "zhumai_youxiao",
                "points": refined_points,
                "group_id": None,
                "shape_type": "linestrip",
                "flags": {}
            }
            json_data["shapes"].append(shape_data)

        # 添加zhumai_zhengti数据
        if zhengti_points:
            shape_data = {
                "label": "zhumai_zhengti",
                "points": zhengti_points,
                "group_id": None,
                "shape_type": "linestrip",
                "flags": {}
            }
            json_data["shapes"].append(shape_data)

        return json_data


    def predictPic(self, img_path, dic_path):
        dic = {}
        if not os.path.exists(img_path):
            print('img not exist!')
        img = cv2.imread(img_path)
        img_height, img_width = img.shape[:2]
        if not os.path.exists(dic_path):
            dic = {"version": "4.5.6", "flags": {}, "shapes": list(), "imagePath": img_path,
                   "imageHeight": img_height, "imageWidth": img_width}
        else:
            with open(dic_path) as f:
                dic = json.load(f)
        res = self.predict(img)
        dic = self.mask2json(res, dic)
        with open(dic_path, mode='w', encoding='utf-8') as file:
            json.dump(dic, file, indent=4)

    def addDic(self, points, dic):
        dic["shapes"].append({"label": self.classes, "points": points, "group_id": None,
                              "shape_type": "polygon", "flags": {}, "other_data": {}})
    def mask2json(self, gray, dic):
        _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        for contour in contours:
            temp = list()
            for point in contour[:]:
                temp.append([int(point[0][0]) / 640, int(point[0][1]) / 640])
            self.addDic(temp, dic)
        return dic
