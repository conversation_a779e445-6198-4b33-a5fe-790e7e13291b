import os
import onnxruntime
import numpy as np
import cv2
import json

def VThin(image, array):
    h,w = image.shape
    NEXT = 1
    for i in range(h):
        for j in range(w):
            if NEXT == 0:
                NEXT = 1
            else:
                M = image[i, j - 1] + image[i, j] + image[i, j + 1] if 0 < j < w - 1 else 1
                if image[i, j] == 0 and M != 0:
                    a = [0] * 9
                    for k in range(3):
                        for l in range(3):
                            if -1 < (i - 1 + k) < h and -1 < (j - 1 + l) < w and image[i - 1 + k, j - 1 + l] == 255:
                                a[k * 3 + l] = 1
                    sum = a[0] * 1 + a[1] * 2 + a[2] * 4 + a[3] * 8 + a[5] * 16 + a[6] * 32 + a[7] * 64 + a[8] * 128
                    image[i, j] = array[sum] * 255
                    if array[sum] == 1:
                        NEXT = 0
    return image


def HThin(image, array):
    h,w = image.shape
    NEXT = 1
    for j in range(w):
        for i in range(h):
            if NEXT == 0:
                NEXT = 1
            else:
                M = image[i - 1, j] + image[i, j] + image[i + 1, j] if 0 < i < h - 1 else 1
                if image[i, j] == 0 and M != 0:
                    a = [0] * 9
                    for k in range(3):
                        for l in range(3):
                            if -1 < (i - 1 + k) < h and -1 < (j - 1 + l) < w and image[i - 1 + k, j - 1 + l] == 255:
                                a[k * 3 + l] = 1
                    sum = a[0] * 1 + a[1] * 2 + a[2] * 4 + a[3] * 8 + a[5] * 16 + a[6] * 32 + a[7] * 64 + a[8] * 128
                    image[i, j] = array[sum] * 255
                    if array[sum] == 1:
                        NEXT = 0
    return image


def Xihua(image,num=10):
    array = [0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1,
             1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1,
             0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1,
             1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1,
             1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
             0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
             1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 1, 0, 1,
             0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
             0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1,
             1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1,
             0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1,
             1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0,
             1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
             1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0,
             1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 1, 0, 0,
             1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 0, 0, 0]
    for i in range(num):
        image=VThin(image, array)
        image=HThin(image, array)
    return image


class Segmentation:
    def __init__(self, onnx_model, classes):
        sess_gpu = onnxruntime.InferenceSession(onnx_model,
                                                providers=['CUDAExecutionProvider', 'CPUExecutionProvider'])
        in_name = [input.name for input in sess_gpu.get_inputs()][0]
        out_name = [output.name for output in sess_gpu.get_outputs()]
        self.session = {'predict': sess_gpu, 'in_name': in_name, 'out_name': out_name}
        self.classes = classes

    def preprocess(self, image):
        image = cv2.resize(np.array(image), (640, 640))
        img = image.copy().astype(np.float32)
        img_mean = [123.675, 116.28, 103.53]
        img_std = [58.395, 57.12, 57.375]
        mean = np.float64(np.array(img_mean).reshape(1, -1))
        stdinv = 1 / np.float64(np.array(img_std).reshape(1, -1))
        cv2.cvtColor(img, cv2.COLOR_BGR2RGB, img)
        cv2.subtract(img, mean, img)
        cv2.multiply(img, stdinv, img)
        img = img.astype(np.float32)
        # img = (image - np.array(img_mean).astype(np.float32)) / np.array(img_std).astype(np.float32)
        img = np.transpose(img, [2, 0, 1])
        img = np.expand_dims(img, axis=0)
        return img

    def _get_model_prediction(self, image, profiler=None):
        """
        内部函数：只进行模型推理，返回原始预测结果

        Args:
            image: 输入图像
            profiler: 性能分析器（可选）

        Returns:
            numpy.ndarray: 模型预测的mask结果 (640x640)
        """
        if profiler:
            profiler.mark_stage("开始模型推理")

        img = self.preprocess(image)
        if profiler:
            profiler.mark_stage("图像预处理完成")

        res1 = self.session['predict'].run(self.session['out_name'], {self.session['in_name']: img})[0]
        if profiler:
            profiler.mark_stage("ONNX模型推理完成")

        res1 = np.squeeze(res1, axis=0)
        res2 = np.squeeze(res1, axis=0)
        print(np.sum(res2==1))

        result = np.uint8(res2 * 255)
        if profiler:
            profiler.mark_stage("推理结果后处理完成")

        return result

    def predict(self, image):
        """
        预测函数：返回mask轮廓点的相对点集

        Args:
            image: 输入图像

        Returns:
            List[List[List[float]]]: 轮廓点列表，每个轮廓包含相对坐标点
        """
        mask = self._get_model_prediction(image)
        return self._mask_to_contour_points(mask)

    def _mask_to_contour_points(self, mask, profiler=None):
        """
        将mask转换为轮廓点的相对点集

        Args:
            mask: 二值化mask (numpy.ndarray)
            profiler: 性能分析器（可选）

        Returns:
            List[List[List[float]]]: 轮廓点列表
        """
        if profiler:
            profiler.mark_stage("开始轮廓检测")

        _, binary = cv2.threshold(mask, 127, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if profiler:
            profiler.mark_stage("轮廓检测完成")

        contour_points_list = []
        for contour in contours:
            points = []
            for point in contour:
                # 转换为相对640*640的比例值
                x_ratio = float(point[0][0]) / 640.0
                y_ratio = float(point[0][1]) / 640.0
                points.append([x_ratio, y_ratio])
            contour_points_list.append(points)

        if profiler:
            profiler.mark_stage("轮廓坐标转换完成")

        return contour_points_list

    def get_zhumai_youxiao(self, image=None, mask=None, profiler=None):
        """
        获取细化后的相对点集（相对640*640的比例值）
        返回格式如JSON中的"zhumai_youxiao"的shape

        Args:
            image: 输入图像（可选，如果提供mask则不需要）
            mask: 预测的mask结果（可选，如果提供则不需要重新预测）
            profiler: 性能分析器（可选）

        Returns:
            List[List[float]]: 细化后的相对坐标点集
        """
        if profiler:
            profiler.mark_stage("开始youxiao处理")

        # 获取mask
        if mask is None:
            if image is None:
                raise ValueError("必须提供image或mask参数之一")
            mask = self._get_model_prediction(image, profiler)

        # 获取轮廓点用于细化处理
        contour_points_list = self._mask_to_contour_points(mask, profiler)

        if not contour_points_list:
            if profiler:
                profiler.mark_stage("youxiao处理完成(无轮廓)")
            return []

        if profiler:
            profiler.mark_stage("开始骨架细化")

        result = self._process_skeleton_refinement(contour_points_list, profiler)

        if profiler:
            profiler.mark_stage("youxiao处理完成")

        return result

    def _process_skeleton_refinement(self, contour_points_list, profiler=None):
        """
        对轮廓点进行骨架细化处理，提取主脉的断开点集

        Args:
            contour_points_list: 轮廓点列表
            profiler: 性能分析器（可选）

        Returns:
            List[List[float]]: 细化后的主脉断开点集（相对坐标）
        """
        if not contour_points_list:
            return []

        if profiler:
            profiler.mark_stage("开始创建细化mask")

        # 合并所有轮廓创建统一的mask进行细化
        combined_mask = np.zeros((640, 640), dtype=np.uint8)

        for contour_points in contour_points_list:
            # 将相对坐标转换回像素坐标
            pixel_points = []
            for point in contour_points:
                x_pixel = int(point[0] * 640)
                y_pixel = int(point[1] * 640)
                pixel_points.append([x_pixel, y_pixel])

            # 在统一mask上绘制轮廓
            if len(pixel_points) > 2:
                pixel_points_array = np.array(pixel_points, dtype=np.int32)
                cv2.fillPoly(combined_mask, [pixel_points_array], 255)

        if profiler:
            profiler.mark_stage("开始Xihua细化算法")

        # 进行细化处理
        combined_mask = cv2.bitwise_not(combined_mask)
        mask_copy = combined_mask.copy()
        refined_mask = Xihua(mask_copy)

        if profiler:
            profiler.mark_stage("Xihua细化算法完成")

        # 提取细化后的骨架点
        black_pixels = np.column_stack(np.where(refined_mask == 0))

        if len(black_pixels) == 0:
            if profiler:
                profiler.mark_stage("骨架细化完成(无骨架点)")
            return []

        if profiler:
            profiler.mark_stage("开始提取主脉线段")

        # 提取主脉结构：识别断开的线段
        main_vessel_points = self._extract_main_vessel_segments(black_pixels, profiler)

        if profiler:
            profiler.mark_stage("骨架细化处理完成")

        return main_vessel_points

    def _extract_main_vessel_segments(self, skeleton_pixels, profiler=None):
        """
        从骨架像素中提取主脉的断开线段

        Args:
            skeleton_pixels: 骨架像素点数组 [[y, x], ...]
            profiler: 性能分析器（可选）

        Returns:
            List[List[float]]: 主脉断开点集的相对坐标
        """
        if len(skeleton_pixels) == 0:
            return []

        if profiler:
            profiler.mark_stage("开始分析骨架方向")

        # 按主要方向排序骨架点
        y_min, x_min = skeleton_pixels.min(axis=0)
        y_max, x_max = skeleton_pixels.max(axis=0)

        if y_max - y_min <= x_max - x_min:
            # 主要是水平方向，按x坐标排序
            sorted_pixels = skeleton_pixels[skeleton_pixels[:, 1].argsort()]
            main_axis = 1  # x轴
        else:
            # 主要是垂直方向，按y坐标排序
            sorted_pixels = skeleton_pixels[skeleton_pixels[:, 0].argsort()]
            main_axis = 0  # y轴

        if profiler:
            profiler.mark_stage("开始识别断点")

        # 识别断点：相邻点距离超过阈值的位置
        break_threshold = 3  # 像素距离阈值
        segments = []
        current_segment = [sorted_pixels[0]]

        for i in range(1, len(sorted_pixels)):
            prev_point = sorted_pixels[i-1]
            curr_point = sorted_pixels[i]

            # 计算主轴方向的距离
            main_axis_distance = abs(curr_point[main_axis] - prev_point[main_axis])

            if main_axis_distance > break_threshold:
                # 发现断点，结束当前线段
                if len(current_segment) >= 2:  # 只保留有意义的线段
                    segments.append(current_segment)
                current_segment = [curr_point]
            else:
                current_segment.append(curr_point)

        # 添加最后一个线段
        if len(current_segment) >= 2:
            segments.append(current_segment)

        if profiler:
            profiler.mark_stage(f"识别到{len(segments)}个主脉线段")

        # 从每个线段中采样关键点，形成断开的主脉点集
        main_vessel_points = []
        for segment in segments:
            # 对每个线段进行采样，保持断开特性
            sampled_points = self._sample_segment_points(segment)
            main_vessel_points.extend(sampled_points)

        if profiler:
            profiler.mark_stage("主脉线段提取完成")

        return main_vessel_points

    def _sample_segment_points(self, segment_pixels):
        """
        从线段中采样关键点

        Args:
            segment_pixels: 线段像素点列表

        Returns:
            List[List[float]]: 采样后的相对坐标点
        """
        if len(segment_pixels) <= 2:
            # 短线段直接返回所有点
            sampled_pixels = segment_pixels
        else:
            # 长线段进行采样：起点、中间点、终点
            sampled_pixels = [
                segment_pixels[0],  # 起点
                segment_pixels[len(segment_pixels)//2],  # 中点
                segment_pixels[-1]  # 终点
            ]

        # 转换为相对坐标
        relative_points = []
        for pixel in sampled_pixels:
            y, x = pixel
            x_ratio = float(x) / 640.0
            y_ratio = float(y) / 640.0
            relative_points.append([x_ratio, y_ratio])

        return relative_points

    def get_zhumai_zhengti(self, image=None, mask=None, youxiao_points=None):
        """
        根据get_zhumai_youxiao的结果进行拟合处理，返回修复后的相对点集
        返回格式如JSON中的"zhumai_zhengti"的shape

        Args:
            image: 输入图像（可选）
            mask: 预测的mask结果（可选）
            youxiao_points: 细化后的点集（可选，如果提供则不需要重新计算）

        Returns:
            List[List[float]]: 拟合修复后的相对坐标点集
        """
        # 获取细化后的点集
        if youxiao_points is None:
            youxiao_points = self.get_zhumai_youxiao(image, mask)

        if not youxiao_points:
            return []

        return self._process_skeleton_fitting(youxiao_points)

    def _process_skeleton_fitting(self, youxiao_points):
        """
        对细化后的骨架点进行拟合修复处理

        Args:
            youxiao_points: 细化后的相对坐标点集

        Returns:
            List[List[float]]: 拟合修复后的相对坐标点集
        """
        # 将相对坐标转换为像素坐标进行处理
        pixel_points = []
        for point in youxiao_points:
            x_pixel = int(point[0] * 640)
            y_pixel = int(point[1] * 640)
            pixel_points.append([y_pixel, x_pixel])  # 注意这里是[y, x]格式

        pixel_points = np.array(pixel_points)

        if len(pixel_points) < 2:
            return youxiao_points

        # 找到断点并进行连接修复
        y_min, x_min = pixel_points.min(axis=0)
        y_max, x_max = pixel_points.max(axis=0)

        chainPoints = []
        if y_max - y_min <= x_max - x_min:
            # 按x坐标排序
            sorted_points = pixel_points[pixel_points[:, 1].argsort()]
            for i in range(len(sorted_points) - 1):
                if sorted_points[i+1][1] - sorted_points[i][1] > 2:
                    chainPoints.append([sorted_points[i], sorted_points[i+1]])
        else:
            # 按y坐标排序
            sorted_points = pixel_points[pixel_points[:, 0].argsort()]
            for i in range(len(sorted_points) - 1):
                if sorted_points[i+1][0] - sorted_points[i][0] > 2:
                    chainPoints.append([sorted_points[i], sorted_points[i+1]])

        # 创建修复后的图像
        repaired_mask = np.zeros((640, 640), dtype=np.uint8)

        # 绘制原始骨架点
        for point in pixel_points:
            y, x = point
            if 0 <= y < 640 and 0 <= x < 640:
                repaired_mask[y, x] = 255

        # 连接断点
        for chain in chainPoints:
            start_point = (int(chain[0][1]), int(chain[0][0]))  # (x, y)
            end_point = (int(chain[1][1]), int(chain[1][0]))    # (x, y)
            cv2.line(repaired_mask, start_point, end_point, 255, 1)

        # 提取修复后的骨架点
        repaired_pixels = np.column_stack(np.where(repaired_mask == 255))

        # 按顺序排列
        if len(repaired_pixels) > 0:
            y_min, x_min = repaired_pixels.min(axis=0)
            y_max, x_max = repaired_pixels.max(axis=0)

            if y_max - y_min <= x_max - x_min:
                repaired_pixels = repaired_pixels[repaired_pixels[:, 1].argsort()]
            else:
                repaired_pixels = repaired_pixels[repaired_pixels[:, 0].argsort()]

        # 转换为相对坐标
        zhengti_points = []
        for pixel in repaired_pixels:
            y, x = pixel
            x_ratio = float(x) / 640.0
            y_ratio = float(y) / 640.0
            zhengti_points.append([x_ratio, y_ratio])

        return zhengti_points

    def get_json_data(self, image, image_path=None):
        """
        组合三种点集数据，生成完整的JSON结构
        ⚡ 性能优化：只进行一次模型推理，避免重复计算

        Args:
            image: 输入图像
            image_path: 图像路径（可选，用于JSON中的imagePath字段）

        Returns:
            dict: 包含三种点集的完整JSON数据结构
        """
        # 获取图像尺寸
        if len(image.shape) == 3:
            img_height, img_width, _ = image.shape
        else:
            img_height, img_width = image.shape

        # ⚡ 关键优化：只进行一次模型推理
        print("🚀 开始模型推理...")
        mask = self._get_model_prediction(image)
        print("✅ 模型推理完成")

        # 基于同一个mask结果获取三种点集数据
        print("📊 处理轮廓数据...")
        contour_points_list = self._mask_to_contour_points(mask)  # zhumaidakai数据

        print("🔍 处理细化数据...")
        refined_points = self.get_zhumai_youxiao(mask=mask)  # zhumai_youxiao数据

        print("🔧 处理拟合数据...")
        zhengti_points = self.get_zhumai_zhengti(mask=mask, youxiao_points=refined_points)  # zhumai_zhengti数据

        # 构建JSON数据结构
        json_data = {
            "version": "5.0.1",
            "flags": {},
            "shapes": [],
            "imagePath": image_path if image_path else "",
            "imageHeight": img_height,
            "imageWidth": img_width
        }

        # 添加zhumaidakai数据（可能有多个轮廓）
        for i, contour_points in enumerate(contour_points_list):
            if contour_points:  # 确保轮廓不为空
                shape_data = {
                    "label": "zhumaidakai",
                    "points": contour_points,
                    "group_id": None,
                    "shape_type": "polygon",
                    "flags": {}
                }
                json_data["shapes"].append(shape_data)

        # 添加zhumai_youxiao数据
        if refined_points:
            shape_data = {
                "label": "zhumai_youxiao",
                "points": refined_points,
                "group_id": None,
                "shape_type": "linestrip",
                "flags": {}
            }
            json_data["shapes"].append(shape_data)

        # 添加zhumai_zhengti数据
        if zhengti_points:
            shape_data = {
                "label": "zhumai_zhengti",
                "points": zhengti_points,
                "group_id": None,
                "shape_type": "linestrip",
                "flags": {}
            }
            json_data["shapes"].append(shape_data)

        print("✅ JSON数据构建完成")
        return json_data


    def predictPic(self, img_path, dic_path):
        dic = {}
        if not os.path.exists(img_path):
            print('img not exist!')
        img = cv2.imread(img_path)
        img_height, img_width = img.shape[:2]
        if not os.path.exists(dic_path):
            dic = {"version": "4.5.6", "flags": {}, "shapes": list(), "imagePath": img_path,
                   "imageHeight": img_height, "imageWidth": img_width}
        else:
            with open(dic_path) as f:
                dic = json.load(f)
        res = self.predict(img)
        dic = self.mask2json(res, dic)
        with open(dic_path, mode='w', encoding='utf-8') as file:
            json.dump(dic, file, indent=4)

    def addDic(self, points, dic):
        dic["shapes"].append({"label": self.classes, "points": points, "group_id": None,
                              "shape_type": "polygon", "flags": {}, "other_data": {}})
    def mask2json(self, gray, dic):
        _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        for contour in contours:
            temp = list()
            for point in contour[:]:
                temp.append([int(point[0][0]) / 640, int(point[0][1]) / 640])
            self.addDic(temp, dic)
        return dic
