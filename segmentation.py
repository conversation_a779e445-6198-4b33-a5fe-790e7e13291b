import os
import onnxruntime
import numpy as np
import cv2
import json

def VThin(image, array):
    h,w = image.shape
    NEXT = 1
    for i in range(h):
        for j in range(w):
            if NEXT == 0:
                NEXT = 1
            else:
                M = image[i, j - 1] + image[i, j] + image[i, j + 1] if 0 < j < w - 1 else 1
                if image[i, j] == 0 and M != 0:
                    a = [0] * 9
                    for k in range(3):
                        for l in range(3):
                            if -1 < (i - 1 + k) < h and -1 < (j - 1 + l) < w and image[i - 1 + k, j - 1 + l] == 255:
                                a[k * 3 + l] = 1
                    sum = a[0] * 1 + a[1] * 2 + a[2] * 4 + a[3] * 8 + a[5] * 16 + a[6] * 32 + a[7] * 64 + a[8] * 128
                    image[i, j] = array[sum] * 255
                    if array[sum] == 1:
                        NEXT = 0
    return image


def HThin(image, array):
    h,w = image.shape
    NEXT = 1
    for j in range(w):
        for i in range(h):
            if NEXT == 0:
                NEXT = 1
            else:
                M = image[i - 1, j] + image[i, j] + image[i + 1, j] if 0 < i < h - 1 else 1
                if image[i, j] == 0 and M != 0:
                    a = [0] * 9
                    for k in range(3):
                        for l in range(3):
                            if -1 < (i - 1 + k) < h and -1 < (j - 1 + l) < w and image[i - 1 + k, j - 1 + l] == 255:
                                a[k * 3 + l] = 1
                    sum = a[0] * 1 + a[1] * 2 + a[2] * 4 + a[3] * 8 + a[5] * 16 + a[6] * 32 + a[7] * 64 + a[8] * 128
                    image[i, j] = array[sum] * 255
                    if array[sum] == 1:
                        NEXT = 0
    return image


def Xihua(image,num=10):
    array = [0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1,
             1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1,
             0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1,
             1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1,
             1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
             0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
             1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 1, 0, 1,
             0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
             0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1,
             1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1,
             0, 0, 1, 1, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1,
             1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0,
             1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
             1, 1, 0, 0, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0,
             1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 1, 0, 0,
             1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 0, 0, 0]
    for i in range(num):
        image=VThin(image, array)
        image=HThin(image, array)
    return image


class Segmentation:
    def __init__(self, onnx_model, classes):
        sess_gpu = onnxruntime.InferenceSession(onnx_model,
                                                providers=['CUDAExecutionProvider', 'CPUExecutionProvider'])
        in_name = [input.name for input in sess_gpu.get_inputs()][0]
        out_name = [output.name for output in sess_gpu.get_outputs()]
        self.session = {'predict': sess_gpu, 'in_name': in_name, 'out_name': out_name}
        self.classes = classes

    def preprocess(self, image):
        image = cv2.resize(np.array(image), (640, 640))
        img = image.copy().astype(np.float32)
        img_mean = [123.675, 116.28, 103.53]
        img_std = [58.395, 57.12, 57.375]
        mean = np.float64(np.array(img_mean).reshape(1, -1))
        stdinv = 1 / np.float64(np.array(img_std).reshape(1, -1))
        cv2.cvtColor(img, cv2.COLOR_BGR2RGB, img)
        cv2.subtract(img, mean, img)
        cv2.multiply(img, stdinv, img)
        img = img.astype(np.float32)
        # img = (image - np.array(img_mean).astype(np.float32)) / np.array(img_std).astype(np.float32)
        img = np.transpose(img, [2, 0, 1])
        img = np.expand_dims(img, axis=0)
        return img

    def predict(self, image):
        img = self.preprocess(image)
        res1 = self.session['predict'].run(self.session['out_name'], {self.session['in_name']: img})[0]
        res1 = np.squeeze(res1, axis=0)
        res2 = np.squeeze(res1, axis=0)
        print(np.sum(res2==1))
        return np.uint8(res2 * 255)

    def zhumaiSmooth(self, image):
        img = self.predict(image)
        height,width,_=image.shape
        img=cv2.bitwise_not(img)
        img1=img.copy()
        res=Xihua(img1)
        black_pixels = np.column_stack(np.where(res == 0))
        y_min, x_min = black_pixels.min(axis=0)
        y_max, x_max = black_pixels.max(axis=0)
        chainPoints=[]
        if(y_max-y_min<=x_max-x_min):
            for i in range(0,len(black_pixels)-1):
                black_pixels = black_pixels[black_pixels[:, 1].argsort()[::1]]
                if black_pixels[i+1][1]-black_pixels[i][1]>2:
                    chainPoints.append([black_pixels[i],black_pixels[i+1]])
        else:
            for i in range(0,len(black_pixels)-1):
                black_pixels = black_pixels[black_pixels[:, 0].argsort()[::1]]
                if black_pixels[i + 1][0] - black_pixels[i][0] > 2:
                    chainPoints.append([black_pixels[i], black_pixels[i + 1]])
        #print(chainPoints)
        for i in range(0,len(chainPoints)):
            cv2.line(image,(chainPoints[i][0][1],chainPoints[i][0][0]),(chainPoints[i][1][1],chainPoints[i][1][0]),(0,255,0),2)
        image=np.ones((640,640,3),np.uint8)
        image=image*255
        image[:,:][res==0]=(0,0,255)
        # cv2.imwrite('5.jpg',image)
        image=cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        return image

   
    def predictPic(self, img_path, dic_path):
        dic = {}
        if not os.path.exists(img_path):
            print('img not exist!')
        img = cv2.imread(img_path)
        img_height, img_width = img.shape[:2]
        if not os.path.exists(dic_path):
            dic = {"version": "4.5.6", "flags": {}, "shapes": list(), "imagePath": img_path,
                   "imageHeight": img_height, "imageWidth": img_width}
        else:
            with open(dic_path) as f:
                dic = json.load(f)
        res = self.predict(img)
        dic = self.mask2json(res, dic)
        with open(dic_path, mode='w', encoding='utf-8') as file:
            json.dump(dic, file, indent=4)

    def addDic(self, points, dic):
        dic["shapes"].append({"label": self.classes, "points": points, "group_id": None,
                              "shape_type": "polygon", "flags": {}, "other_data": {}})
    def mask2json(self, gray, dic):
        _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        for contour in contours:
            temp = list()
            for point in contour[:]:
                temp.append([int(point[0][0]) / 640, int(point[0][1]) / 640])
            self.addDic(temp, dic)
        return dic
