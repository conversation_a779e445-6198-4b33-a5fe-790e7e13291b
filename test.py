import os
from detection import <PERSON>lov8
from segmentation import Segmentation
modelPath=r'F:\2024_04'
#kaohong=Yolov8(modelPath+'/yolov8_new.onnx',0.4,0.3,"kaohong")
zhimaiqing=Segmentation(modelPath+'/zhumai.onnx',"zhumai")
img_path='F:/2024_04/VOCdevkit/VOC2012/JPEGImages/'
files=os.listdir(img_path)
numbers=len(files)
number=1
for file in files:
    print(str(number)+'/'+str(numbers))
    img = img_path + file
    #kaohong.predictPic(img, img_path + file.split('.')[0] + '.json')
    zhimaiqing.predictPic(img, img_path + file.split('.')[0] + '.json')
    number+=1